@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&family=Open+Sans:wght@400;500;600&family=Poppins:wght@400;500;600;700&family=Roboto:wght@400;500;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Open Sans', 'Roboto', sans-serif;
    font-size: 14px;
  }

  h1, h2, h3, h4, h5, h6 {
    font-family: 'Montserrat', 'Poppins', sans-serif;
  }
}

@layer components {
  .btn-primary {
    @apply bg-night-blue text-white px-4 py-2 rounded-md hover:bg-opacity-90 transition-all;
  }

  .btn-secondary {
    @apply bg-gold text-night-blue px-4 py-2 rounded-md hover:bg-opacity-90 transition-all;
  }

  .btn-danger {
    @apply bg-alert-red text-white px-4 py-2 rounded-md hover:bg-opacity-90 transition-all;
  }

  .card {
    @apply bg-white rounded-lg shadow-md p-6;
  }

  .input-field {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-night-blue focus:border-transparent;
  }
}
